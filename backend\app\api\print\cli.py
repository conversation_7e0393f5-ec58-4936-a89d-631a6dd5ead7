"""
CLI file management endpoints
- POST /cli/upload/{drum_id}: Upload and cache CLI file for a specific drum (production path).
- POST /cli/upload: Upload a CLI for preview only (non-drum-specific; dev/testing convenience).
- GET /cli/{file_id}/layer/{layer_num}/preview: Render a preview PNG for a specific layer from preview cache.
- POST /cli/{file_id}/layer/{layer_num}/send/{drum_id}: Send a single layer from preview cache to a drum (dev/testing).
- POST /cli/{file_id}/layers/send/{drum_id}: Send a layer range from preview cache to a drum (dev/testing).
"""
import logging
import uuid
import asyncio
from fastapi import APIRouter, HTTPException, Depends, Response, Path, UploadFile, File, Body

# Getter functions from below
from app.dependencies import get_multilayer_job_manager
from infrastructure.cli_editor import Editor, CliParsingError
from infrastructure.recoater_client.exceptions import RecoaterConnectionError, RecoaterAPIError
from app.dependencies import get_recoater_client

from .models import (
    CliUploadResponse,
    LayerRangeRequest,
)

from app.services.job_management import MultiMaterialJobService

logger = logging.getLogger(__name__)
router = APIRouter()

# Preview-only: Upload and parse a multi-layer CLI file (not tied to any drum)
@router.post("/cli/upload", response_model=CliUploadResponse)
async def upload_cli_file_for_preview(
    file: UploadFile = File(...),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> CliUploadResponse:
    """Upload and parse a CLI file for preview; caches by generated file_id."""
    try:
        if not file.filename or not file.filename.lower().endswith('.cli'):
            raise HTTPException(status_code=400, detail="Only .cli files are supported")

        file_bytes = await file.read()
        if len(file_bytes) == 0:
            raise HTTPException(status_code=400, detail="Uploaded file is empty")

        parser = Editor()
        # Offload CPU-bound CLI parsing to a thread to avoid blocking the event loop
        parsed = await asyncio.to_thread(parser.parse, file_bytes)

        file_id = uuid.uuid4().hex
        job_manager.add_cli_file(file_id, parsed, file.filename)

        return CliUploadResponse(
            success=True,
            message=f"CLI file {file.filename} uploaded for preview",
            file_id=file_id,
            total_layers=len(parsed.layers),
            file_size=len(file_bytes),
            filename=file.filename,
        )
    except HTTPException:
        raise
    except CliParsingError as e:
        # Parsing issues are client errors
        raise HTTPException(status_code=400, detail=f"CLI parsing error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error uploading CLI file for preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# Read uploaded file
# Use cli_editor to parse the file data
# Cache ParsedCliFile for selected Drum in job_manager
@router.post("/cli/upload/{drum_id}", response_model=CliUploadResponse)
async def upload_cli_file_to_drum(
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2) to upload the CLI file to"),
    file: UploadFile = File(...),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> CliUploadResponse:
    """Upload and cache CLI file for a specific drum."""
    try:
        logger.info(f"Uploading CLI file {file.filename} to drum {drum_id}")
        if not file.filename or not file.filename.lower().endswith('.cli'):
            raise HTTPException(status_code=400, detail="Only .cli files are supported")

        file_data = await file.read()
        if len(file_data) == 0:
            raise HTTPException(status_code=400, detail="Uploaded file is empty")

        parser = Editor()
        # Offload CPU-bound CLI parsing to a thread to avoid blocking the event loop
        parsed_data = await asyncio.to_thread(parser.parse, file_data)

        # Cache the file for the specific drum
        job_manager.cache_cli_file_for_drum(drum_id, parsed_data, file.filename)

        logger.info(
            f"CLI file cached successfully for drum {drum_id}. Layers: {len(parsed_data.layers)}"
        )

        return CliUploadResponse(
            success=True,
            message=f"CLI file {file.filename} uploaded and cached for drum {drum_id}",
            file_id=f"drum_{drum_id}",  # Simple identifier for drum-cached files
            total_layers=len(parsed_data.layers),
            file_size=len(file_data),
            filename=file.filename,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error uploading CLI file to drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# Acquires the CLI file stored in multilayer_job_manager
# Navigates to desired layer
# Uses cli_parser to render the layer to png for display
@router.get("/cli/{file_id}/layer/{layer_num}/preview")
async def get_cli_layer_preview(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    layer_num: int = Path(..., ge=1, description="The layer number to preview (1-based)"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
) -> Response:
    """Get a preview of a specific layer from a parsed CLI file as PNG image."""
    try:
        logger.info(f"Getting CLI layer preview for file {file_id}, layer {layer_num}")
        cache_entry = job_manager.get_cli_file_with_metadata(file_id)
        if cache_entry is None:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        parsed_data = cache_entry['parsed_file']

        layer_index = layer_num - 1
        if layer_index < 0 or layer_index >= len(parsed_data.layers):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid layer number. Must be between 1 and {len(parsed_data.layers)}",
            )

        layer = parsed_data.layers[layer_index]
        parser = Editor()
        png_bytes = parser.render_layer_to_png(layer)

        logger.info(f"CLI layer {layer_num} rendered successfully for file {file_id}")
        return Response(
            content=png_bytes,
            media_type="image/png",
            headers={"Content-Disposition": f"inline; filename=cli_layer_{layer_num}_preview.png"},
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting CLI layer preview: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# Send a single layer from a preview-cached CLI file to a specific drum
@router.post("/cli/{file_id}/layer/{layer_num}/send/{drum_id}")
async def send_cli_layer_to_drum(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    layer_num: int = Path(..., ge=1, description="The layer number to send (1-based)"),
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2) to send the layer to"),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
    recoater_client=Depends(get_recoater_client),
):
    try:
        cache_entry = job_manager.get_cli_file_with_metadata(file_id)
        if cache_entry is None:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        parsed_data = cache_entry["parsed_file"]
        layer_index = layer_num - 1
        if layer_index < 0 or layer_index >= len(parsed_data.layers):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid layer number. Must be between 1 and {len(parsed_data.layers)}",
            )

        layer = parsed_data.layers[layer_index]
        parser = Editor()
        cli_bytes = parser.generate_single_layer_ascii_cli(layer=layer, header_lines=parsed_data.header_lines)

        # Upload to recoater
        upload_result = recoater_client.upload_drum_geometry(
            drum_id=drum_id,
            file_data=cli_bytes,
            content_type="application/octet-stream",
        )

        # Also cache the single-layer CLI for this drum so subsequent jobs use the correct layer count (1)
        try:
            parsed_single = Editor().parse(cli_bytes)
            original_name = cache_entry.get('original_filename') or 'file.cli'
            job_manager.cache_cli_file_for_drum(
                drum_id,
                parsed_single,
                f"layer_{layer_num}_{original_name}"
            )
        except Exception as _e:
            logger.warning(f"Failed to cache single-layer CLI for drum {drum_id}: {_e}")

        return {
            "success": True,
            "message": f"Layer {layer_num} sent to drum {drum_id} successfully",
            "file_id": file_id,
            "layer_num": layer_num,
            "drum_id": drum_id,
            "layer_z_height": getattr(layer, "z_height", None),
            "data_size": len(cli_bytes),
            "upload_result": upload_result,
        }
    except RecoaterConnectionError as e:
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except CliParsingError as e:
        raise HTTPException(status_code=400, detail=f"CLI generation error: {e}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error sending CLI layer: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")


# Send a layer range from a preview-cached CLI file to a specific drum
@router.post("/cli/{file_id}/layers/send/{drum_id}")
async def send_cli_layer_range_to_drum(
    file_id: str = Path(..., description="The unique ID of the uploaded CLI file"),
    drum_id: int = Path(..., ge=0, le=2, description="The drum's ID (0-2) to send layers to"),
    body: LayerRangeRequest = Body(...),
    job_manager: MultiMaterialJobService = Depends(get_multilayer_job_manager),
    recoater_client=Depends(get_recoater_client),
):
    try:
        cache_entry = job_manager.get_cli_file_with_metadata(file_id)
        if cache_entry is None:
            raise HTTPException(status_code=404, detail="CLI file not found. Please upload the file first.")

        parsed_data = cache_entry["parsed_file"]
        total_layers = len(parsed_data.layers)

        start_layer = body.start_layer
        end_layer = body.end_layer

        if start_layer > end_layer:
            raise HTTPException(status_code=400, detail="Start layer cannot be greater than end layer")
        if end_layer > total_layers:
            raise HTTPException(status_code=400, detail=f"Invalid layer range. Must be between 1 and {total_layers}")

        # 1-based to 0-based indices
        start_idx = start_layer - 1
        end_idx = end_layer - 1

        selected_layers = parsed_data.layers[start_idx : end_idx + 1]
        parser = Editor()
        cli_bytes = parser.generate_ascii_cli_from_layer_range(
            layers=selected_layers, header_lines=parsed_data.header_lines
        )

        # Upload to recoater
        upload_result = recoater_client.upload_drum_geometry(
            drum_id=drum_id,
            file_data=cli_bytes,
            content_type="application/octet-stream",
        )

        # Also cache the trimmed CLI for this drum so subsequent jobs use the correct layer count
        try:
            parsed_trimmed = Editor().parse(cli_bytes)
            original_name = cache_entry.get('original_filename') or 'file.cli'
            job_manager.cache_cli_file_for_drum(
                drum_id,
                parsed_trimmed,
                f"layers_{start_layer}-{end_layer}_{original_name}"
            )
        except Exception as _e:
            logger.warning(f"Failed to cache trimmed CLI for drum {drum_id}: {_e}")

        z_start = getattr(parsed_data.layers[start_idx], "z_height", None)
        z_end = getattr(parsed_data.layers[end_idx], "z_height", None)

        return {
            "success": True,
            "message": f"Layer range {start_layer}-{end_layer} sent to drum {drum_id} successfully",
            "file_id": file_id,
            "drum_id": drum_id,
            "layer_range": [start_layer, end_layer],
            "z_height_range": [z_start, z_end],
            "data_size": len(cli_bytes),
            "upload_result": upload_result,
        }
    except RecoaterConnectionError as e:
        raise HTTPException(status_code=503, detail=f"Connection error: {e}")
    except RecoaterAPIError as e:
        raise HTTPException(status_code=400, detail=f"API error: {e}")
    except CliParsingError as e:
        raise HTTPException(status_code=400, detail=f"CLI generation error: {e}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error sending CLI layer range: {e}")
        raise HTTPException(status_code=500, detail=f"Internal error: {e}")
