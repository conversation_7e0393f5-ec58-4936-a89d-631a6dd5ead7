import json
import logging
from fastapi import WebSocket, WebSocketDisconnect

from app.services.communication.websocket_manager import WebSocketConnectionManager

logger = logging.getLogger(__name__)

class WebSocketHandler:
    """Handles WebSocket connections and message processing."""
    
    def __init__(self, websocket_manager: WebSocketConnectionManager):
        self.websocket_manager = websocket_manager
    
    async def websocket_endpoint(self, websocket: WebSocket):
        """WebSocket endpoint for real-time status updates."""
        await self.websocket_manager.connect(websocket)
        try:
            while True:
                message_text = await websocket.receive_text()
                await self.handle_websocket_message(websocket, message_text)
        except WebSocketDisconnect:
            logger.info(f"WebSocket client disconnected. Remaining connections: {self.websocket_manager.connection_count - 1}")
            self.websocket_manager.disconnect(websocket)
    
    async def handle_websocket_message(self, websocket: WebSocket, message_text: str):
        """Process incoming WebSocket messages for subscription updates."""
        try:
            message = json.loads(message_text)
            if message.get("type") == "subscribe" and "data_types" in message:
                self.websocket_manager.update_subscription(websocket, message["data_types"])
                logger.info(f"Updated subscription: {message['data_types']}")
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON received from WebSocket: {message_text}")
        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")