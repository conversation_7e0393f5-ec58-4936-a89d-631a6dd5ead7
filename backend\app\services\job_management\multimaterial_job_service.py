"""
Multi-Material Job Service
--------------------------

Unified service for multi-material 3D printing job management following the
7-variable OPC UA architecture. This service orchestrates the complete
layer-by-layer printing workflow with proper OPC UA coordination.

Architecture:
- LayerProcessingMixin: Job lifecycle and layer upload operations
- OPCUACoordinationMixin: OPC UA variable management and coordination
- CliCachingMixin: CLI file caching for both generic and drum-specific operations

7-Variable OPC UA Workflow:
1. job_active: Backend sets TRUE at start, FALSE at end
2. total_layers: Backend sets once at job start
3. current_layer: Backend manages, PLC reads
4. recoater_ready_to_print: Backend writes when Aerosint is ready
5. recoater_layer_complete: Back<PERSON> writes when deposition complete
6. backend_error: Backend writes if any issue arises
7. plc_error: PLC writes if any issues

Primary Workflow:
1. Validate and setup job state with max layers
2. Setup OPC UA job_active=True, total_layers=max_layers, current_layer=1
3. For each layer: reset flags → upload to drums → signal ready → wait for completion → signal complete
4. Update current_layer progress after each layer
5. Set job_active=False and cleanup on completion

Public Methods:
- start_layer_by_layer_job(): Main workflow entry point
- clear_all_error_flags(): Clear all error flags (job-level and OPC UA-level)
- Plus all methods from inherited mixins (cancel_job, get_job_status, etc.)

Private Helper Methods:
- _validate_and_setup_job(): Validate job state and setup initial configuration
- _process_all_layers(): Process all layers in the job sequentially
- _process_layer(layer_index): Process a single layer (upload, print, complete)
- _upload_layer_to_drums(layer_index): Upload layer data to all enabled drums
- _get_layer_data_for_drum(drum_id, layer_index): Get CLI data for specific drum/layer
- _get_empty_layer_template(): Get empty layer template for missing drum data
- _prepare_and_start_print_job(layer_index): Prepare and signal layer ready for printing
"""

from __future__ import annotations

import logging
import asyncio
from typing import Dict, Optional, Any

from infrastructure.recoater_client import RecoaterClient
from app.models.multilayer_job import MultiMaterialJobState

from .models import CliCacheEntry, MultiMaterialJobError
from .mixins.layer_operations_mixin import LayerProcessingMixin
from .mixins.coordination_mixin import OPCUACoordinationMixin
from .mixins.cli_caching_mixin import CliCachingMixin
from app.config.job_config import get_job_config, JobConfig

logger = logging.getLogger(__name__)

class MultiMaterialJobService(LayerProcessingMixin, OPCUACoordinationMixin, CliCachingMixin):
    """
    Unified service that provides a cohesive interface for multi-material jobs.

    This concrete class inherits consolidated mixins that provide:
    - LayerProcessingMixin: Job lifecycle and layer upload operations
    - OPCUACoordinationMixin: OPC UA variable management and coordination
    - CliCachingMixin: CLI file caching for both generic and drum-specific operations
    """

    def __init__(self, recoater_client: RecoaterClient, opcua=None, job_config: Optional[JobConfig] = None):
        """
        Initialize the multi-material job service.

        Args:
            recoater_client: Hardware client for drum uploads and print control
            opcua: Injected OPC UA service instance (for testability and DI)
            job_config: Injected JobConfig (env-driven); if None, will be loaded
        """
        self.recoater_client = recoater_client
        self.opcua = opcua  # Injected OPC UA service (may be None)
        self.job_config = job_config or get_job_config()

        # Initialize state fields required by mixins
        self.cli_parser = __import__('infrastructure.cli_editor.editor', fromlist=['Editor']).Editor()
        self.current_job: Optional[MultiMaterialJobState] = None
        self.cli_cache: Dict[str, CliCacheEntry] = {}
        self.drum_cli_cache: Dict[int, Dict[str, Any]] = {i: None for i in range(self.job_config.MAX_DRUMS)}

        # Background task reference for proper cancellation
        self._background_task: Optional[asyncio.Task] = None

        # Configuration from job config
        self.drum_upload_delay = self.job_config.DRUM_UPLOAD_DELAY_SECONDS
        self.status_poll_interval = self.job_config.LAYER_COMPLETION_QUERY_INTERVAL_SECONDS

        logger.info("MultiMaterialJobService initialized with consolidated mixins")

    async def start_layer_by_layer_job(self) -> bool:
        """
        Start layer-by-layer printing job following the exact 7-variable OPC UA workflow.

        Workflow:
        1. Validate and setup job state while acquiring max layers
        2. Setup OPC UA: job_active=True, total_layers=max_layers, current_layer=1
        3. Process all layers sequentially
        4. Cleanup: job_active=False, reset flags

        Returns:
            True if job completed successfully
        """
        try:
            max_layers = await self._validate_and_setup_job()
            await self.setup_opcua_job(max_layers)

            # Mark in-memory job active and running (used for internal cancellation checks)
            if self.current_job:
                self.current_job.is_active = True
                try:
                    from app.models.multilayer_job import JobStatus
                    self.current_job.status = JobStatus.RUNNING
                    # Start at layer 1 for consistency with OPC UA progress
                    self.current_job.current_layer = 1
                except Exception:
                    pass

            await self._process_all_layers()

            if self.current_job:
                self.current_job.mark_completed()
                # On successful completion: clear drum caches and fully reset in-memory job state
                try:
                    if hasattr(self, 'clear_drum_cache'):
                        self.clear_drum_cache()
                        logger.info("Cleared all drum cache files after job completion")
                except Exception as _e:
                    error_message = f"Clearing drum cache after completion failed: {_e}"
                    logger.error(error_message)
                    await self.handle_job_error(RuntimeError(error_message))
                # Preserve job_id for logging, then clear current_job to avoid stale 'printing' UI state
                try:
                    job_id = getattr(self.current_job, 'job_id', None)
                except Exception:
                    job_id = None
                self.current_job = None

            logger.info("Layer-by-layer job completed successfully")
            return True

        except Exception as e:
            logger.error(f"Layer-by-layer job failed: {e}")
            await self.handle_job_error(e)
            raise
        finally:
            await self.cleanup_opcua_job()
            # Clear the background task reference when job completes or fails
            self._background_task = None

    async def _validate_and_setup_job(self) -> int:
        """Validate cached files and setup job state. Returns max layers."""
        if not self.has_cached_files():
            error_message = "No CLI files cached for any drums"
            logger.error(error_message)
            await self.handle_job_error(RuntimeError(error_message))
            raise MultiMaterialJobError(error_message)

        max_layers = self.get_max_layers()
        logger.info(f"Starting layer-by-layer job with {max_layers} layers")

        # Create job state. If drum has cached data: file_mapping = {0: drum_0_cache, ...}
        file_mapping = {}
        for drum_id, drum_data in self.drum_cli_cache.items():
            if drum_data is not None:
                file_mapping[drum_id] = f"drum_{drum_id}_cache"

        # Initialize job state
        self.current_job = MultiMaterialJobState(
            file_ids=file_mapping,
            total_layers=max_layers
        )

        return max_layers

    async def _process_all_layers(self):
        """
        Process all layers sequentially following the exact workflow.

        For each layer:
        - Reset OPC UA layer flags
        - Upload layer data to each drum (with 2s delay between drums)
        - Signal recoater ready to print
        - Start print job
        - Wait for layer completion via polling
        - Signal layer complete
        - Update current layer progress
        """
        max_layers = self.get_max_layers()

        for layer_index in range(max_layers):
            # Respect cancellation before starting a new layer
            if getattr(self, 'current_job', None) and not self.current_job.is_active:
                logger.info("Job cancelled; stopping before processing next layer")
                return

            layer_num = layer_index + 1
            logger.info(f"Processing layer {layer_num}/{max_layers}")

            # Process this layer
            await self._process_layer(layer_index)

            # If cancelled during the layer, exit gracefully
            if getattr(self, 'current_job', None) and not self.current_job.is_active:
                logger.info("Job cancelled; exiting after current layer")
                return

            # Update current layer progress (cap at total_layers to avoid >100%)
            next_progress = min(layer_num + 1, max_layers)
            await self.update_opcua_layer_progress(next_progress)

        logger.info("All layers processed successfully")

    async def _process_layer(self, layer_index: int):
        """
        Process a single layer with pause/resume on error until success.

        Sequence on each attempt:
        1. Reset OPC UA layer flags (recoater_ready_to_print=False, recoater_layer_complete=False)
        2. Upload layer data to each drum (with 2s delay between drums)
        3. Signal recoater ready to print (recoater_ready_to_print=True)
        4. Start print job
        5. Wait for layer completion via polling loop
        6. On success: Signal layer complete (recoater_layer_complete=True)
           On error: wait for operator to clear error flags, then retry the layer
        """
        layer_num = layer_index + 1
        while True:
            # Respect cancellation promptly
            if getattr(self, 'current_job', None) and not self.current_job.is_active:
                logger.info(f"Job cancelled; skipping remaining work for layer {layer_num}")
                return

            await self.reset_opcua_layer_flags()
            await self._upload_layer_to_drums(layer_index)

            # Check cancellation again before starting print
            if getattr(self, 'current_job', None) and not self.current_job.is_active:
                logger.info(f"Job cancelled before starting print on layer {layer_num}")
                return

            await self._prepare_and_start_print_job(layer_index)
            success = await self.wait_for_layer_completion()

            # If cancelled during wait, exit without retry
            if getattr(self, 'current_job', None) and not self.current_job.is_active:
                logger.info(f"Job cancelled during layer {layer_num} wait; exiting layer loop")
                return

            if success:
                await self.signal_opcua_layer_complete()
                return
            # Error case: pause until errors cleared, then retry
            logger.warning(f"Layer {layer_num} encountered an error. Waiting for operator to clear error flags to retry...")
            # Wait until OPC UA error flags are cleared (backend_error/plc_error False)
            try:
                await self.wait_until_error_cleared(getattr(self, 'status_poll_interval', 1.0))
            except Exception as e:
                # Small backoff to avoid tight loop in pathological cases
                logger.error(f"Error waiting for error flags to clear on layer {layer_num}: {e}")
                await self.handle_job_error(e)
                await asyncio.sleep(getattr(self, 'status_poll_interval', 1.0))


    async def _upload_layer_to_drums(self, layer_index: int):
        """Upload layer data to each drum with delay between uploads."""
        import asyncio
        from infrastructure.recoater_client.exceptions import RecoaterConnectionError, RecoaterAPIError

        for drum_id in range(self.job_config.MAX_DRUMS):
            cli_data = await self._get_layer_data_for_drum(drum_id, layer_index)

            # Upload to drum and delay to prevent overloading
            # Use the correct API for both real and mock clients (upload_drum_geometry)
            # Offload sync client call to a thread to avoid blocking the event loop.
            try:
                await asyncio.to_thread(self.recoater_client.upload_drum_geometry, drum_id, cli_data, "text/plain")
                logger.debug(f"Uploaded layer {layer_index + 1} to drum {drum_id}")
            except RecoaterConnectionError as e:
                error_message = f"Layer {layer_index + 1} upload failed to Drum {drum_id} - Connection Error: {str(e)}"
                logger.error(error_message)
                await self.handle_job_error(RuntimeError(error_message))
                raise
            except RecoaterAPIError as e:
                # Extract more detailed API error information
                error_details = str(e)
                if "status" in error_details.lower() and ":" in error_details:
                    # Try to extract HTTP status code from RecoaterAPIError message
                    status_info = error_details.split(":", 1)[0] if ":" in error_details else "Unknown status"
                    response_info = error_details.split(":", 1)[1].strip() if ":" in error_details else str(e)
                    error_message = f"Layer {layer_index + 1} upload failed to Drum {drum_id} - API Error [{status_info}]: {response_info}"
                else:
                    error_message = f"Layer {layer_index + 1} upload failed to Drum {drum_id} - API Error: {str(e)}"
                logger.error(error_message)
                await self.handle_job_error(RuntimeError(error_message))
                raise
            except Exception as e:
                error_message = f"Layer {layer_index + 1} upload failed to Drum {drum_id} - Unexpected Error: {str(e)}"
                logger.error(error_message)
                await self.handle_job_error(RuntimeError(error_message))
                raise
            
            if drum_id < (self.job_config.MAX_DRUMS - 1):
                await asyncio.sleep(self.drum_upload_delay)

    async def _get_layer_data_for_drum(self, drum_id: int, layer_index: int) -> bytes:
        """Get the appropriate layer data for a specific drum."""
        drum_data = self.drum_cli_cache[drum_id]

        if drum_data is not None and layer_index < drum_data['layer_count']:
            # Use actual layer from cached file
            layer = drum_data['parsed_file'].layers[layer_index]
            return self.cli_parser.generate_single_layer_ascii_cli(
                layer=layer,
                header_lines=drum_data['parsed_file'].header_lines,
            )
        else:
            # Use empty layer template
            return await self._get_empty_layer_template()

    async def _get_empty_layer_template(self) -> bytes:
        """Get the empty layer template from configured absolute path."""
        try:
            with open(self.job_config.EMPTY_LAYER_TEMPLATE_PATH, 'rb') as f:
                return f.read()
        except Exception as e:
            error_message = f"Empty layer template not found or unreadable at: {self.job_config.EMPTY_LAYER_TEMPLATE_PATH} ({e})"
            logger.error(error_message)
            await self.handle_job_error(RuntimeError(error_message))
            raise MultiMaterialJobError(error_message)

    async def _prepare_and_start_print_job(self, layer_index: int):
        """Signal recoater ready and start the print job for this layer."""
        # Signal recoater ready
        await self.signal_opcua_ready_to_print()

        # Start print job
        self.recoater_client.start_print_job()
        logger.info(f"Started printing layer {layer_index + 1}")

    # ---- Unified Error Handling ----

    async def clear_all_error_flags(self) -> bool:
        """
        Clear all error flags using unified error handling pattern.

        This method coordinates between job-level and OPC UA-level error clearing
        to ensure consistent error state management across the system.

        Returns:
            True if all error flags were cleared successfully
        """
        try:
            # Clear job-specific error flags
            job_cleared = await self.clear_job_error_flags()

            # Clear OPC UA error flags (backend_error, plc_error)
            opcua_cleared = await self.clear_error_flags()

            success = job_cleared and opcua_cleared
            if success:
                logger.info("All error flags cleared successfully")
            else:
                logger.warning("Some error flags failed to clear")
            return success

        except Exception as e:
            logger.error(f"Failed to clear all error flags: {e}")
            await self.handle_job_error(e)
            return False


__all__ = [
    "MultiMaterialJobService",
    "MultiMaterialJobError",
    "CliCacheEntry",
]

