"""
CoordinationMixin
=================

Business-logic helpers for coordinating job state via the variable store.
Implements the coordination layer between domain logic and infrastructure.

Clean Architecture Role: Use Case / Application Service Layer
Responsibilities:
- Connection state management (logical coordination layer)
- High-level business operations: job lifecycle, layer progress, error handling
- Event subscription system: in-process callbacks for variable changes
- Variable orchestration: coordinating multiple related variable updates
- Error state management: unified error flag handling across backend/PLC

Key Business Operations:
- Job Lifecycle: set_job_active/inactive with proper state transitions
- Layer Coordination: update_layer_progress, recoater ready/complete signaling
- Error Management: set_backend_error, set_plc_error, clear_error_flags
- Event System: subscribe_to_changes, _trigger_event_handlers for reactive programming

Architecture Pattern: Mixin composition with delegation to ServerMixin
Dependencies: ServerMixin for low-level variable access, MonitoringMixin for health checks

Public Methods:
- connect(): Establish coordination connection and start monitoring
- disconnect(): Close coordination connection and cleanup
- is_connected(): Check logical connection status
- set_job_active(total_layers): Initialize job with total layers and current_layer=1
- set_job_inactive(): Deactivate job and clear all coordination flags
- update_layer_progress(current_layer): Update current layer progress
- set_recoater_ready_to_print(ready): Signal recoater readiness to PLC
- set_recoater_layer_complete(complete): Signal layer completion to PLC
- set_backend_error(error): Set/clear backend error flag
- set_plc_error(error): Set/clear PLC error flag
- clear_error_flags(): Clear both backend and PLC error flags
- write_variable(name, value): Write variable with event triggering
- read_variable(name): Read variable value
- subscribe_to_changes(variables, handler): Subscribe to variable change events

Private Helper Methods:
- _trigger_event_handlers(name, value): Execute subscribed event handlers
"""
from __future__ import annotations

import asyncio
import logging
from typing import Any, Callable, Optional, Dict, List


class CoordinationMixin:
    _logger: logging.Logger
    _connected: bool = False
    _monitoring_task: Optional[asyncio.Task] = None
    _event_handlers: Dict[str, List[Callable]]

    def __init__(self, *args, **kwargs):
        # Initialize event handlers if not already initialized
        if not hasattr(self, '_event_handlers'):
            self._event_handlers = {}
        super().__init__(*args, **kwargs)

    # Connection management (logical)
    async def connect(self) -> bool:
        try:
            if self._connected:
                return True
            # Ensure server is running
            if not getattr(self, "is_server_running") or not self.is_server_running:
                ok = await self.start_server()
                if not ok:
                    return False
            self._connected = True
            # Initialize event handlers if not already initialized
            if not hasattr(self, '_event_handlers') or self._event_handlers is None:
                self._event_handlers = {}
            # Start lightweight monitoring
            if hasattr(self, "start_monitoring"):
                await self.start_monitoring()
            self._logger.info("OPC UA coordination connected")
            return True
        except Exception as e:
            self._logger.error(f"Failed to connect coordination: {e}")
            return False

    async def disconnect(self) -> bool:
        try:
            self._connected = False
            # Stop monitoring if present
            if hasattr(self, "stop_monitoring"):
                await self.stop_monitoring()
            await self.stop_server()
            self._logger.info("OPC UA coordination disconnected")
            return True
        except Exception as e:
            self._logger.error(f"Failed to disconnect coordination: {e}")
            return False

    def is_connected(self) -> bool:
        return bool(self._connected)

    # High-level helpers using variable store
    async def set_job_active(self, total_layers: int) -> bool:
        ok1 = await self.write_variable("job_active", True)
        ok2 = await self.write_variable("total_layers", int(total_layers))
        ok3 = await self.write_variable("current_layer", 1)
        return ok1 and ok2 and ok3

    async def set_job_inactive(self) -> bool:
        ok = True
        ok &= await self.write_variable("job_active", False)
        ok &= await self.write_variable("recoater_ready_to_print", False)
        ok &= await self.write_variable("recoater_layer_complete", False)
        ok &= await self.write_variable("backend_error", False)
        ok &= await self.write_variable("plc_error", False)
        ok &= await self.write_variable("current_layer", 0)
        ok &= await self.write_variable("total_layers", 0)
        return ok

    async def update_layer_progress(self, current_layer: int) -> bool:
        return await self.write_variable("current_layer", int(current_layer))

    async def set_recoater_ready_to_print(self, ready: bool) -> bool:
        return await self.write_variable("recoater_ready_to_print", bool(ready))

    async def set_recoater_layer_complete(self, complete: bool) -> bool:
        return await self.write_variable("recoater_layer_complete", bool(complete))

    async def set_backend_error(self, error: bool) -> bool:
        return await self.write_variable("backend_error", bool(error))

    async def set_plc_error(self, error: bool) -> bool:
        return await self.write_variable("plc_error", bool(error))

    async def clear_error_flags(self) -> bool:
        ok1 = await self.set_backend_error(False)
        ok2 = await self.set_plc_error(False)
        return ok1 and ok2

    # Variable access delegates to ServerMixin + triggers handlers
    async def write_variable(self, name: str, value: Any) -> bool:  # type: ignore[override]
        if hasattr(super(), "write_variable"):
            ok = await super().write_variable(name, value)  # type: ignore
            if ok:
                await self._trigger_event_handlers(name, value)
            return ok
        return False

    async def read_variable(self, name: str) -> Any:  # type: ignore[override]
        if hasattr(super(), "read_variable"):
            return await super().read_variable(name)  # type: ignore
        return None

    async def subscribe_to_changes(self, variables: list[str], handler: Callable) -> bool:
        try:
            # Ensure event handlers are initialized
            if not hasattr(self, "_event_handlers") or self._event_handlers is None:
                self._event_handlers = {}
            for v in variables:
                self._event_handlers.setdefault(v, []).append(handler)
            self._logger.debug(f"Subscribed to changes for variables: {variables}")
            return True
        except Exception as e:
            self._logger.error(f"Failed to subscribe to changes: {e}")
            return False

    async def _trigger_event_handlers(self, name: str, value: Any) -> None:
        try:
            if not hasattr(self, "_event_handlers") or self._event_handlers is None:
                return
            handlers = self._event_handlers.get(name, [])
            self._logger.debug(f"Triggering {len(handlers)} handlers for variable '{name}' with value: {value}")
            for h in handlers:
                try:
                    if asyncio.iscoroutinefunction(h):
                        await h(name, value)
                    else:
                        h(name, value)
                except Exception as eh:
                    self._logger.warning(f"Event handler error for {name}: {eh}")
        except Exception as e:
            self._logger.error(f"Error triggering event handlers for {name}: {e}")


