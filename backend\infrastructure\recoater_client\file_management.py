"""
File Management Methods
======================

This module contains file management methods for the RecoaterClient.
These methods implement the drum geometry file management endpoints from the openapi.json specification.
"""

from typing import Dict, Any
from .exceptions import RecoaterConnectionError, RecoaterAPIError


class FileManagementMixin:
    """Mixin class providing file management methods for RecoaterClient."""

    def upload_drum_geometry(self, drum_id: int, file_data: bytes, content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """
        Upload geometry file (PNG or CLI) to a specific drum.

        Args:
            drum_id: The ID of the drum to upload geometry to
            file_data: The binary file data to upload
            content_type: The content type of the file (default: application/octet-stream)

        Returns:
            Response from the API
            
        Raises:
            RecoaterConnectionError: If connection to recoater fails
            RecoaterAPIError: If API returns error or file upload fails
        """
        try:
            headers = {"Content-Type": content_type}
            response = self._make_request("PUT", f"/drums/{drum_id}/geometry", data=file_data, headers=headers)
            return response
        except ConnectionError as e:
            raise RecoaterConnectionError(f"Issue: Connection timeout during file upload to Drum {drum_id}")
        except TimeoutError as e:
            raise RecoaterConnectionError(f"Issue: Upload timeout - file transfer to Drum {drum_id} took too long")
        except Exception as e:
            # Check if it's a file size or format issue
            if "too large" in str(e).lower():
                raise RecoaterAPIError(f"Issue: File too large for upload to Drum {drum_id}")
            elif "invalid format" in str(e).lower() or "unsupported" in str(e).lower():
                raise RecoaterAPIError(f"Issue: Invalid file format for Drum {drum_id} (expected PNG or CLI)")
            elif "space" in str(e).lower() or "storage" in str(e).lower():
                raise RecoaterAPIError(f"Issue: Insufficient storage space on Drum {drum_id}")
            else:
                raise RecoaterAPIError(f"Issue: File upload failed to Drum {drum_id} - {str(e)}")

    def download_drum_geometry(self, drum_id: int) -> bytes:
        """
        Download geometry file from a specific drum as PNG image.

        Args:
            drum_id: The ID of the drum to download geometry from

        Returns:
            PNG image data as bytes
        """
        response = self._make_request("GET", f"/drums/{drum_id}/geometry", return_raw=True)
        return response.content

    def delete_drum_geometry(self, drum_id: int) -> Dict[str, Any]:
        """
        Delete geometry file from a specific drum.

        Args:
            drum_id: The ID of the drum to delete geometry from

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/geometry")
