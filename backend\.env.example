# Recoater HMI Backend Configuration
# ===================================

# Recoater Hardware API Configuration
RECOATER_API_HOST=*************
RECOATER_API_PORT=8080
RECOATER_API_BASE_URL=http://*************:8080

# WebSocket Configuration
WEBSOCKET_POLL_INTERVAL=1.0

# Development Configuration
DEBUG=true
LOG_LEVEL=INFO

# Use mock recoater in development mode
DEVELOPMENT_MODE=true
# Mock recoater timing and failure injection (dev-only)
MOCK_TIMING_DRUM_DELAY_MS=2000
MOCK_TIMING_LAYER_TIME_MS=4000
MOCK_FAIL_AT_LAYER=0
MOCK_FAIL_TYPE=none
MOCK_RANDOM_FAILURE_PROB=0.0
MOCK_SEED=123

# Job Orchestration Settings (all values in seconds unless noted)
# Number of enabled drums (addressed 0..JOB_MAX_DRUMS-1)
JOB_MAX_DRUMS=3
# Cache cleanup retention window for preview/drum caches
JOB_CACHE_CLEANUP_MAX_AGE_SECONDS=3600

# Delay between uploading the same layer to each drum (prevents overload)
JOB_DRUM_UPLOAD_DELAY_SECONDS=2.0
# Interval between status polling cycles during a layer print
JOB_STATUS_POLL_INTERVAL_SECONDS=2.0
# Maximum time to wait for recoater "ready-to-print" signal per layer
JOB_READY_TIMEOUT_SECONDS=30.0
# Maximum time to wait for a layer to complete printing before erroring
JOB_COMPLETION_TIMEOUT_SECONDS=300.0
# Relative or absolute path to the blank/empty ASCII CLI template used for missing layers
JOB_EMPTY_LAYER_TEMPLATE_PATH=app/templates/empty_layer.cli

